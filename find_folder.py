#!/usr/bin/env python3
"""
搜索指定目录下的特定文件夹
快速找到所有匹配的文件夹并显示完整路径
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path


def find_folders_by_name(root_dir, folder_name, case_sensitive=False):
    """
    在指定目录下搜索特定名称的文件夹
    
    Args:
        root_dir: 根目录路径
        folder_name: 要搜索的文件夹名称
        case_sensitive: 是否区分大小写
        
    Returns:
        list: 找到的文件夹路径列表
    """
    found_folders = []
    
    try:
        # 使用系统的 find 命令进行快速搜索
        if case_sensitive:
            # 区分大小写
            cmd = f'find "{root_dir}" -type d -name "{folder_name}" 2>/dev/null'
        else:
            # 不区分大小写
            cmd = f'find "{root_dir}" -type d -iname "{folder_name}" 2>/dev/null'
        
        print(f"正在搜索文件夹: {folder_name}")
        print(f"搜索目录: {root_dir}")
        print("请稍候...")
        
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            paths = result.stdout.strip().split('\n')
            for path in paths:
                if path.strip():  # 忽略空行
                    found_folders.append(path.strip())
        else:
            print("搜索命令执行失败")
            
    except subprocess.TimeoutExpired:
        print("搜索超时，目录可能包含大量文件")
        return []
    except Exception as e:
        print(f"搜索时发生错误: {e}")
        return []
    
    return found_folders


def get_folder_info(folder_path):
    """
    获取文件夹的基本信息
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        dict: 包含文件夹信息的字典
    """
    try:
        stat_info = os.stat(folder_path)
        
        # 计算文件夹大小（只计算直接子文件，不递归）
        total_size = 0
        file_count = 0
        dir_count = 0
        
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path):
                    file_count += 1
                    total_size += os.path.getsize(item_path)
                elif os.path.isdir(item_path):
                    dir_count += 1
        except PermissionError:
            pass
        
        return {
            'size': total_size,
            'file_count': file_count,
            'dir_count': dir_count,
            'modified_time': stat_info.st_mtime
        }
    except Exception:
        return {
            'size': 0,
            'file_count': 0,
            'dir_count': 0,
            'modified_time': 0
        }


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


def format_time(timestamp):
    """格式化时间显示"""
    import datetime
    try:
        dt = datetime.datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return "未知"


def main():
    parser = argparse.ArgumentParser(
        description="搜索指定目录下的特定文件夹",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 find_folder.py "/Volumes/Lexar SSD ARES 4TB Media/LACIE" "SD卡备份"
  python3 find_folder.py "/path/to/search" "folder_name" --case-sensitive
  python3 find_folder.py "/path/to/search" "folder_name" --detailed
        """
    )
    
    parser.add_argument(
        "directory",
        help="要搜索的根目录路径"
    )
    
    parser.add_argument(
        "folder_name",
        help="要搜索的文件夹名称"
    )
    
    parser.add_argument(
        "--case-sensitive",
        action="store_true",
        help="区分大小写搜索"
    )
    
    parser.add_argument(
        "--detailed",
        action="store_true",
        help="显示详细信息（文件数量、大小等）"
    )
    
    args = parser.parse_args()
    
    # 验证目录路径
    root_dir = os.path.abspath(args.directory)
    if not os.path.exists(root_dir):
        print(f"错误: 目录不存在 - {root_dir}")
        sys.exit(1)
    
    if not os.path.isdir(root_dir):
        print(f"错误: 路径不是目录 - {root_dir}")
        sys.exit(1)
    
    # 搜索文件夹
    found_folders = find_folders_by_name(root_dir, args.folder_name, args.case_sensitive)
    
    if not found_folders:
        print(f"\n❌ 未找到名为 '{args.folder_name}' 的文件夹")
        return
    
    print(f"\n✅ 找到 {len(found_folders)} 个匹配的文件夹:")
    print("=" * 80)
    
    for i, folder_path in enumerate(found_folders, 1):
        print(f"\n{i}. {folder_path}")
        
        if args.detailed:
            info = get_folder_info(folder_path)
            print(f"   📁 子文件夹: {info['dir_count']} 个")
            print(f"   📄 文件数量: {info['file_count']} 个")
            print(f"   💾 直接文件大小: {format_size(info['size'])}")
            print(f"   🕒 修改时间: {format_time(info['modified_time'])}")
    
    print(f"\n🎉 搜索完成！总共找到 {len(found_folders)} 个匹配的文件夹")


if __name__ == "__main__":
    main()
