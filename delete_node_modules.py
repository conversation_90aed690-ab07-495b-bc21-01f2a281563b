#!/usr/bin/env python3
"""
安全删除指定目录下的 node_modules 文件夹
只删除名字完全匹配 "node_modules" 的文件夹，确保不会误删其他文件夹
采用逐个查找和删除的方式，避免一次性搜索大量文件导致的性能问题
"""

import os
import sys
import shutil
import argparse
import subprocess
from pathlib import Path


def is_node_modules_folder(path):
    """
    严格检查是否为 node_modules 文件夹

    Args:
        path: 文件夹路径

    Returns:
        bool: 如果是 node_modules 文件夹返回 True，否则返回 False
    """
    if not os.path.isdir(path):
        return False

    folder_name = os.path.basename(path)
    # 严格匹配名字，必须完全等于 "node_modules"
    return folder_name == "node_modules"


def find_one_node_modules_folder(root_dir):
    """
    在指定目录下查找第一个 node_modules 文件夹
    使用系统的 find 命令，找到第一个就返回

    Args:
        root_dir: 根目录路径

    Returns:
        str or None: 找到的第一个 node_modules 文件夹路径，没找到返回 None
    """
    try:
        # 使用 find 命令，找到第一个就停止 (-quit 参数)
        result = subprocess.run(
            f'find "{root_dir}" -type d -name "node_modules" -print -quit 2>/dev/null',
            shell=True,
            capture_output=True,
            text=True,
            timeout=60  # 1分钟超时
        )

        if result.returncode == 0 and result.stdout.strip():
            path = result.stdout.strip()
            # 双重检查确保安全
            if is_node_modules_folder(path):
                return path

        return None

    except subprocess.TimeoutExpired:
        print("搜索超时")
        return None
    except Exception as e:
        print(f"搜索时发生错误: {e}")
        return None


def delete_node_modules_folder(folder_path):
    """
    安全删除单个 node_modules 文件夹

    Args:
        folder_path: 要删除的文件夹路径

    Returns:
        bool: 删除成功返回 True，失败返回 False
    """
    # 最后一次安全检查
    if not is_node_modules_folder(folder_path):
        print(f"安全检查失败: {folder_path} 不是有效的 node_modules 文件夹")
        return False

    try:
        print(f"正在删除: {folder_path}")
        shutil.rmtree(folder_path)
        print(f"✓ 成功删除: {folder_path}")
        return True
    except PermissionError:
        print(f"✗ 权限不足，无法删除: {folder_path}")
        return False
    except Exception as e:
        print(f"✗ 删除失败: {folder_path} - {e}")
        return False


def auto_delete_mode(root_dir):
    """
    自动删除模式：逐个查找和删除 node_modules 文件夹，无需确认

    Args:
        root_dir: 根目录路径

    Returns:
        int: 删除成功的文件夹数量
    """
    deleted_count = 0
    total_size_saved = 0

    print(f"开始在 {root_dir} 中自动查找和删除所有 node_modules 文件夹...")
    print("找到即删除，无需确认\n")

    while True:
        print("正在搜索下一个 node_modules 文件夹...")

        # 查找下一个 node_modules 文件夹
        folder_path = find_one_node_modules_folder(root_dir)

        if not folder_path:
            print("✓ 没有找到更多的 node_modules 文件夹")
            break

        # 显示找到的文件夹信息
        folder_size = get_folder_size(folder_path)
        print(f"\n找到: {folder_path}")
        print(f"大小: {format_size(folder_size)}")

        # 自动删除
        if delete_node_modules_folder(folder_path):
            deleted_count += 1
            total_size_saved += folder_size
            print(f"已删除 {deleted_count} 个文件夹，累计节省空间: {format_size(total_size_saved)}")

        print("-" * 80)

    return deleted_count, total_size_saved


def main():
    parser = argparse.ArgumentParser(
        description="安全删除指定目录下的 node_modules 文件夹",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 delete_node_modules.py /path/to/your/projects
  python3 delete_node_modules.py . --batch
        """
    )

    parser.add_argument(
        "directory",
        help="要搜索的根目录路径"
    )

    parser.add_argument(
        "--batch",
        action="store_true",
        help="批量模式：一次性查找所有文件夹并确认删除（较慢但完整）"
    )

    args = parser.parse_args()

    # 验证目录路径
    target_dir = os.path.abspath(args.directory)
    if not os.path.exists(target_dir):
        print(f"错误: 目录不存在 - {target_dir}")
        sys.exit(1)

    if not os.path.isdir(target_dir):
        print(f"错误: 路径不是目录 - {target_dir}")
        sys.exit(1)

    print(f"目标目录: {target_dir}")

    if args.batch:
        # 批量模式（原来的方式）
        print("使用批量模式...")
        batch_delete_mode(target_dir)
    else:
        # 自动删除模式（新的方式）
        print("使用自动删除模式...")
        deleted_count, total_size_saved = auto_delete_mode(target_dir)
        print(f"\n🎉 删除完成！")
        print(f"总共删除了 {deleted_count} 个 node_modules 文件夹")
        print(f"总共节省了 {format_size(total_size_saved)} 的磁盘空间")


def batch_delete_mode(target_dir):
    """
    批量删除模式：一次性查找所有文件夹
    """
    print("正在搜索所有 node_modules 文件夹...")

    # 使用简化的批量搜索
    try:
        result = subprocess.run(
            f'find "{target_dir}" -type d -name "node_modules" 2>/dev/null',
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )

        if result.returncode != 0:
            print("搜索失败")
            return

        paths = [p.strip() for p in result.stdout.strip().split('\n') if p.strip()]
        node_modules_folders = [p for p in paths if is_node_modules_folder(p)]

    except subprocess.TimeoutExpired:
        print("搜索超时，请尝试使用交互式模式")
        return
    except Exception as e:
        print(f"搜索时发生错误: {e}")
        return

    if not node_modules_folders:
        print("未找到任何 node_modules 文件夹")
        return

    print(f"\n找到 {len(node_modules_folders)} 个 node_modules 文件夹:")
    for i, folder in enumerate(node_modules_folders[:10]):  # 只显示前10个
        folder_size = get_folder_size(folder)
        print(f"  {i+1}. {folder} ({format_size(folder_size)})")

    if len(node_modules_folders) > 10:
        print(f"  ... 还有 {len(node_modules_folders) - 10} 个文件夹")

    # 确认删除
    print(f"\n警告: 即将永久删除以上 {len(node_modules_folders)} 个 node_modules 文件夹")
    print("这个操作不可撤销！")

    while True:
        response = input("确认删除吗？(yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            break
        elif response in ['no', 'n']:
            print("操作已取消")
            return
        else:
            print("请输入 'yes' 或 'no'")

    # 执行删除
    print(f"\n开始删除 {len(node_modules_folders)} 个 node_modules 文件夹...")

    success_count = 0
    for i, folder in enumerate(node_modules_folders):
        print(f"[{i+1}/{len(node_modules_folders)}] ", end="")
        if delete_node_modules_folder(folder):
            success_count += 1

    print(f"\n删除完成: {success_count}/{len(node_modules_folders)} 个文件夹删除成功")


def get_folder_size(folder_path):
    """获取文件夹大小（字节）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except (OSError, PermissionError):
        pass
    return total_size


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


if __name__ == "__main__":
    main()
