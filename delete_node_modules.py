#!/usr/bin/env python3
"""
安全删除指定目录下的 node_modules 文件夹
只删除名字完全匹配 "node_modules" 的文件夹，确保不会误删其他文件夹
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


def is_node_modules_folder(path):
    """
    严格检查是否为 node_modules 文件夹
    
    Args:
        path: 文件夹路径
        
    Returns:
        bool: 如果是 node_modules 文件夹返回 True，否则返回 False
    """
    if not os.path.isdir(path):
        return False
    
    folder_name = os.path.basename(path)
    # 严格匹配名字，必须完全等于 "node_modules"
    return folder_name == "node_modules"


def find_node_modules_folders(root_dir):
    """
    在指定目录下查找所有 node_modules 文件夹
    
    Args:
        root_dir: 根目录路径
        
    Returns:
        list: node_modules 文件夹路径列表
    """
    node_modules_folders = []
    
    try:
        for root, dirs, files in os.walk(root_dir):
            # 检查当前目录下的所有子目录
            for dir_name in dirs:
                if dir_name == "node_modules":
                    full_path = os.path.join(root, dir_name)
                    # 双重检查确保安全
                    if is_node_modules_folder(full_path):
                        node_modules_folders.append(full_path)
    except PermissionError as e:
        print(f"权限错误: 无法访问某些目录 - {e}")
    except Exception as e:
        print(f"搜索过程中发生错误: {e}")
    
    return node_modules_folders


def delete_node_modules_folder(folder_path):
    """
    安全删除单个 node_modules 文件夹
    
    Args:
        folder_path: 要删除的文件夹路径
        
    Returns:
        bool: 删除成功返回 True，失败返回 False
    """
    # 最后一次安全检查
    if not is_node_modules_folder(folder_path):
        print(f"安全检查失败: {folder_path} 不是有效的 node_modules 文件夹")
        return False
    
    try:
        print(f"正在删除: {folder_path}")
        shutil.rmtree(folder_path)
        print(f"✓ 成功删除: {folder_path}")
        return True
    except PermissionError:
        print(f"✗ 权限不足，无法删除: {folder_path}")
        return False
    except Exception as e:
        print(f"✗ 删除失败: {folder_path} - {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="安全删除指定目录下的所有 node_modules 文件夹",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 delete_node_modules.py /path/to/your/projects
  python3 delete_node_modules.py . --dry-run
  python3 delete_node_modules.py ~/Documents/projects --confirm
        """
    )
    
    parser.add_argument(
        "directory",
        help="要搜索的根目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="仅显示将要删除的文件夹，不实际删除"
    )
    
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="跳过确认提示，直接删除"
    )
    
    args = parser.parse_args()
    
    # 验证目录路径
    target_dir = os.path.abspath(args.directory)
    if not os.path.exists(target_dir):
        print(f"错误: 目录不存在 - {target_dir}")
        sys.exit(1)
    
    if not os.path.isdir(target_dir):
        print(f"错误: 路径不是目录 - {target_dir}")
        sys.exit(1)
    
    print(f"搜索目录: {target_dir}")
    print("正在查找 node_modules 文件夹...")
    
    # 查找所有 node_modules 文件夹
    node_modules_folders = find_node_modules_folders(target_dir)
    
    if not node_modules_folders:
        print("未找到任何 node_modules 文件夹")
        return
    
    print(f"\n找到 {len(node_modules_folders)} 个 node_modules 文件夹:")
    for folder in node_modules_folders:
        folder_size = get_folder_size(folder)
        print(f"  - {folder} ({format_size(folder_size)})")
    
    # Dry run 模式
    if args.dry_run:
        print(f"\n[DRY RUN] 将删除以上 {len(node_modules_folders)} 个文件夹")
        return
    
    # 确认删除
    if not args.confirm:
        print(f"\n警告: 即将永久删除以上 {len(node_modules_folders)} 个 node_modules 文件夹")
        print("这个操作不可撤销！")
        
        while True:
            response = input("确认删除吗？(yes/no): ").lower().strip()
            if response in ['yes', 'y']:
                break
            elif response in ['no', 'n']:
                print("操作已取消")
                return
            else:
                print("请输入 'yes' 或 'no'")
    
    # 执行删除
    print(f"\n开始删除 {len(node_modules_folders)} 个 node_modules 文件夹...")
    
    success_count = 0
    for folder in node_modules_folders:
        if delete_node_modules_folder(folder):
            success_count += 1
    
    print(f"\n删除完成: {success_count}/{len(node_modules_folders)} 个文件夹删除成功")


def get_folder_size(folder_path):
    """获取文件夹大小（字节）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
    except (OSError, PermissionError):
        pass
    return total_size


def format_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


if __name__ == "__main__":
    main()
