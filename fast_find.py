#!/usr/bin/env python3
"""
高效搜索文件夹 - 使用多种方法确保找到目标文件夹
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def method1_find_command(root_dir, folder_name):
    """方法1: 使用 find 命令（不区分大小写）"""
    print(f"🔍 方法1: 使用 find 命令搜索...")
    try:
        # 不区分大小写搜索
        cmd = f'find "{root_dir}" -type d -iname "*{folder_name}*" 2>/dev/null'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0 and result.stdout.strip():
            paths = [p.strip() for p in result.stdout.strip().split('\n') if p.strip()]
            return paths
    except Exception as e:
        print(f"   方法1失败: {e}")
    return []


def method2_mdfind_command(folder_name):
    """方法2: 使用 macOS 的 mdfind 命令（Spotlight搜索）"""
    print(f"🔍 方法2: 使用 Spotlight 搜索...")
    try:
        cmd = f'mdfind "kMDItemDisplayName == \'*{folder_name}*\'c" 2>/dev/null'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0 and result.stdout.strip():
            paths = [p.strip() for p in result.stdout.strip().split('\n') if p.strip() and os.path.isdir(p.strip())]
            return paths
    except Exception as e:
        print(f"   方法2失败: {e}")
    return []


def method3_python_walk(root_dir, folder_name):
    """方法3: 使用 Python os.walk（逐层搜索，可中断）"""
    print(f"🔍 方法3: 使用 Python 逐层搜索...")
    found_paths = []
    search_count = 0
    
    try:
        for root, dirs, files in os.walk(root_dir):
            search_count += 1
            
            # 每搜索100个目录显示一次进度
            if search_count % 100 == 0:
                print(f"   已搜索 {search_count} 个目录... 当前: {root}")
            
            # 检查当前目录下的所有子目录
            for dir_name in dirs:
                if folder_name.lower() in dir_name.lower():
                    full_path = os.path.join(root, dir_name)
                    found_paths.append(full_path)
                    print(f"   ✅ 找到: {full_path}")
            
            # 如果已经找到一些结果，可以选择继续或停止
            if len(found_paths) >= 5:  # 找到5个就先返回
                print(f"   已找到 {len(found_paths)} 个结果，继续搜索...")
                
    except KeyboardInterrupt:
        print(f"   搜索被中断，已找到 {len(found_paths)} 个结果")
    except Exception as e:
        print(f"   方法3出错: {e}")
    
    return found_paths


def method4_ls_recursive(root_dir, folder_name):
    """方法4: 使用 ls -R 命令"""
    print(f"🔍 方法4: 使用 ls -R 搜索...")
    try:
        cmd = f'ls -R "{root_dir}" 2>/dev/null | grep -i "{folder_name}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            return [line.strip() for line in lines if line.strip()]
    except Exception as e:
        print(f"   方法4失败: {e}")
    return []


def search_folder(root_dir, folder_name):
    """综合搜索：使用多种方法"""
    print(f"🎯 开始搜索文件夹: '{folder_name}'")
    print(f"📁 搜索目录: {root_dir}")
    print("=" * 80)
    
    all_results = []
    
    # 方法1: find 命令
    results1 = method1_find_command(root_dir, folder_name)
    if results1:
        print(f"   ✅ 找到 {len(results1)} 个结果")
        all_results.extend(results1)
    else:
        print(f"   ❌ 未找到结果")
    
    print()
    
    # 方法2: Spotlight 搜索
    results2 = method2_mdfind_command(folder_name)
    if results2:
        # 过滤出在目标目录下的结果
        filtered_results2 = [p for p in results2 if p.startswith(root_dir)]
        if filtered_results2:
            print(f"   ✅ 找到 {len(filtered_results2)} 个结果")
            all_results.extend(filtered_results2)
        else:
            print(f"   ❌ 在目标目录下未找到结果")
    else:
        print(f"   ❌ 未找到结果")
    
    print()
    
    # 如果前两种方法都没找到，使用Python搜索
    if not all_results:
        print("前两种方法未找到结果，使用Python逐层搜索...")
        results3 = method3_python_walk(root_dir, folder_name)
        all_results.extend(results3)
    
    # 去重
    unique_results = list(set(all_results))
    
    return unique_results


def main():
    if len(sys.argv) < 3:
        print("使用方法: python3 fast_find.py <搜索目录> <文件夹名称>")
        print("示例: python3 fast_find.py '/Volumes/Lexar SSD ARES 4TB Media/LACIE' 'SD卡备份'")
        sys.exit(1)
    
    root_dir = sys.argv[1]
    folder_name = sys.argv[2]
    
    # 验证目录
    if not os.path.exists(root_dir):
        print(f"❌ 错误: 目录不存在 - {root_dir}")
        sys.exit(1)
    
    if not os.path.isdir(root_dir):
        print(f"❌ 错误: 路径不是目录 - {root_dir}")
        sys.exit(1)
    
    start_time = time.time()
    
    # 开始搜索
    results = search_folder(root_dir, folder_name)
    
    end_time = time.time()
    search_time = end_time - start_time
    
    print("\n" + "=" * 80)
    print("🎉 搜索完成！")
    print(f"⏱️  搜索耗时: {search_time:.2f} 秒")
    
    if results:
        print(f"✅ 找到 {len(results)} 个匹配的文件夹:")
        print("-" * 80)
        
        for i, path in enumerate(results, 1):
            print(f"{i:2d}. {path}")
            
            # 显示文件夹基本信息
            try:
                if os.path.exists(path):
                    items = os.listdir(path)
                    dirs = [item for item in items if os.path.isdir(os.path.join(path, item))]
                    files = [item for item in items if os.path.isfile(os.path.join(path, item))]
                    print(f"     📁 {len(dirs)} 个子文件夹, 📄 {len(files)} 个文件")
                else:
                    print(f"     ⚠️  路径可能不存在或无法访问")
            except PermissionError:
                print(f"     🔒 权限不足，无法查看内容")
            except Exception as e:
                print(f"     ❌ 获取信息失败: {e}")
            
            print()
    else:
        print(f"❌ 未找到名为 '{folder_name}' 的文件夹")
        print("\n💡 建议:")
        print("1. 检查文件夹名称是否正确")
        print("2. 检查是否有权限访问该目录")
        print("3. 尝试搜索部分名称，如 'SD' 或 '备份'")


if __name__ == "__main__":
    main()
